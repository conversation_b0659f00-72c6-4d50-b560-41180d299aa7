import java.net.InetAddress;
import java.net.UnknownHostException;

public class HelloWorld {

    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }

    public static String getCurrentTime() {
        // 获取当前时间，以毫秒为单位
        String currentTime = String.valueOf(System.currentTimeMillis());

        // 打印当前时间
        System.out.println("Current time: " + currentTime);

        
        // 打印 "Hello, World!" 五次
        for (int i = 0; i < 5; i++) {
            System.out.println("Hello, World!");
        }

        return currentTime;
    }

    public static String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            String ipAddress = localHost.getHostAddress();
            return "Local IP address: " + ipAddress;
        } catch (UnknownHostException e) {
            return "Unable to determine the IP address of the local host";
        }
    }
}
